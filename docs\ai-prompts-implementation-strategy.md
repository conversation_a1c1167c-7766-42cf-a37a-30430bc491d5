# AI Dude Prompt System - Implementation Strategy

## Overview

This document outlines the specific code changes and implementation steps required to integrate the AI Dude prompt methodology from `docs/ai-prompts example.md` into our existing AI tool directory system.

## Implementation Summary

### What We're Building

1. **Enhanced Prompt Templates**: Specialized templates following the AI Dude methodology
2. **Database Schema Integration**: Mapping example JSON structure to our tools table
3. **AI Generation Pipeline**: Modified content generation using the proven prompt pattern
4. **Admin Interface**: Enhanced prompt management for AI Dude templates
5. **Validation System**: Quality assurance for AI Dude generated content

### Key Benefits

- **Proven Methodology**: Uses the exact prompt structure from the working example
- **Consistent Tone**: Ensures irreverent, engaging content across all tools
- **Better Quality**: Enhanced validation and quality scoring
- **Seamless Integration**: Works alongside existing system without breaking changes

## Database Schema Modifications

### 1. Prompt Template Storage

The existing `system_configuration` table already supports prompt templates. We need to add AI Dude specific templates:

```sql
-- AI Dude System Prompt Template
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_system_v1', '{
  "name": "AI Dude Content Generation System",
  "description": "System prompt defining AI Dude persona and JSON schema requirements",
  "category": "content",
  "promptType": "system", 
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:\n\n{DATABASE_SCHEMA}\n\n**Tone rules:**\n- Always write like a snarky, witty \"AI Dude.\"\n- Keep it punchy: no corporate sugarcoating.\n- Use contractions, slang, and street-smart humor.\n- Never apologize or say \"I'\''m sorry.\"\n- Write \"description\" as a one-sentence hook.\n- Ensure \"category_confidence\" is 0.90 or above if the category mapping is obvious; if you'\''re guessing, use 0.80 or 0.75.\n\n**VERY IMPORTANT:**\n- Output exactly one JSON object.\n- Do not wrap it in backticks or code fences.\n- Do not add extra fields or comments.\n- If any section is missing in the Markdown, leave that JSON field as an empty string (\"\"), empty array ([]), or appropriate default (\"unknown\").\n- Always format dates as YYYY-MM-DD.\n\nNow read the user content and produce the JSON.",
  "variables": ["DATABASE_SCHEMA"],
  "validationRules": ["Schema compliance", "Tone consistency", "Format requirements"],
  "formatRequirements": "Pure JSON output matching database schema",
  "usage": 0
}', 'prompt_template', 'AI Dude methodology system prompt');

-- AI Dude User Prompt Template (Raw Content)
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_user_v1', '{
  "name": "AI Dude Content Generation User",
  "description": "User prompt for AI Dude content generation (raw content)",
  "category": "content",
  "promptType": "user",
  "template": "{scrapedContent}",
  "variables": ["scrapedContent"],
  "validationRules": [],
  "formatRequirements": "Raw scraped content for AI processing",
  "usage": 0
}', 'prompt_template', 'AI Dude methodology user prompt');
```

### 2. JSON Schema Mapping

Our database schema mapping for the AI Dude JSON structure:

| AI Dude Field | Database Field | Type | Notes |
|---------------|----------------|------|-------|
| `name` | `name` | varchar(255) | Tool name |
| `description` | `description` | text | Short description |
| `detailed_description` | `detailed_description` | text | Long description |
| `features` | `features` | jsonb | Feature array |
| `pros_and_cons` | `pros_and_cons` | jsonb | Pros/cons object |
| `pricing` | `pricing` | jsonb | Pricing information |
| `category_primary` | `category_id` | varchar | Primary category |
| `category_secondary` | `subcategory` | varchar | Secondary category |
| `faqs` | `faqs` | jsonb | FAQ array |
| `hashtags` | `hashtags` | jsonb | Hashtag array |
| `tooltip` | `tooltip` | varchar(255) | Short tooltip |
| `haiku` | `haiku` | jsonb | Haiku object |
| `social_links` | `social_links` | jsonb | Social links |
| `releases` | `releases` | jsonb | Release info |

## Code Changes Required

### 1. Enhanced PromptManager (`src/lib/ai/prompt-manager.ts`)

Add these methods to the existing PromptManager class:

```typescript
/**
 * Build AI Dude system prompt with schema injection
 */
static buildAIDudeSystemPrompt(databaseSchema: any): string {
  const schemaString = JSON.stringify(databaseSchema, null, 2);
  
  return `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

${schemaString}

**Tone rules:**
- Always write like a snarky, witty "AI Dude."
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say "I'm sorry."
- Write "description" as a one-sentence hook.
- Ensure "category_confidence" is 0.90 or above if the category mapping is obvious; if you're guessing, use 0.80 or 0.75.

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing in the Markdown, leave that JSON field as an empty string (""), empty array ([]), or appropriate default ("unknown").
- Always format dates as YYYY-MM-DD.

Now read the user content and produce the JSON.`;
}

/**
 * Build AI Dude user prompt (raw content only)
 */
static buildAIDudeUserPrompt(scrapedContent: string, toolUrl: string): string {
  return scrapedContent; // Pure content as per example methodology
}

/**
 * Get database schema for AI Dude prompts
 */
static getAIDudeDatabaseSchema(): any {
  return {
    name: "string",
    description: "string (under 500 chars)",
    detailed_description: "string (150-300 words)",
    features: ["array of 3-8 feature strings"],
    pricing: {
      type: "Free|Paid|Freemium|Open Source",
      plans: [{"name": "string", "price": "string", "features": ["array"]}]
    },
    pros_and_cons: {
      pros: ["array of 3-10 pros"],
      cons: ["array of 3-10 cons"]
    },
    category_primary: "string",
    category_secondary: "string",
    category_confidence: "number (0.0-1.0)",
    faqs: [{"question": "string", "answer": "string"}],
    hashtags: ["array of 5-10 hashtags"],
    tooltip: "string (under 100 chars)",
    haiku: {
      lines: ["line1", "line2", "line3"],
      theme: "string"
    },
    meta_keywords: ["array of SEO keywords"],
    releases: [{"version": "string", "releaseDate": "YYYY-MM-DD", "changes": ["array"]}],
    social_links: {
      twitter: "url or null",
      linkedin: "url or null",
      github: "url or null"
    }
  };
}

/**
 * Process AI Dude response and map to database schema
 */
static processAIDudeResponse(aiResponse: any): any {
  const mapped = {
    name: aiResponse.name || '',
    description: aiResponse.description || '',
    detailed_description: aiResponse.detailed_description || '',
    features: aiResponse.features || [],
    pricing: aiResponse.pricing || { type: 'unknown', plans: [] },
    pros_and_cons: aiResponse.pros_and_cons || { pros: [], cons: [] },
    category_id: aiResponse.category_primary || null,
    subcategory: aiResponse.category_secondary || null,
    faqs: aiResponse.faqs || [],
    hashtags: aiResponse.hashtags || [],
    tooltip: aiResponse.tooltip || '',
    haiku: aiResponse.haiku || { lines: [], theme: '' },
    social_links: aiResponse.social_links || {},
    releases: aiResponse.releases || []
  };

  // Add AI Dude metadata
  mapped.ai_generation_metadata = {
    methodology: 'ai_dude',
    category_confidence: aiResponse.category_confidence || 0.5,
    generated_at: new Date().toISOString(),
    schema_version: '1.0'
  };

  return mapped;
}
```

### 2. Enhanced AIContentGenerator (`src/lib/ai/content-generator.ts`)

Add this method to the existing AIContentGenerator class:

```typescript
/**
 * Generate content using AI Dude methodology
 */
async generateContentAIDude(
  scrapedContent: string,
  toolUrl: string,
  options: GenerationOptions = {}
): Promise<GenerationResult> {
  try {
    // Build AI Dude prompts
    const systemPrompt = PromptManager.buildAIDudeSystemPrompt(
      PromptManager.getAIDudeDatabaseSchema()
    );
    const userPrompt = PromptManager.buildAIDudeUserPrompt(scrapedContent, toolUrl);

    // Select optimal model
    const modelConfig = ModelSelector.selectOptimalModel({
      contentSize: PromptManager.calculateTokenCount(scrapedContent),
      complexity: options.complexity || 'medium',
      priority: options.priority || 'quality',
      features: ['json_output', 'large_context']
    });

    // Generate content
    const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;
    
    const response = await client.generateContent(systemPrompt, userPrompt, {
      model: modelConfig.model,
      responseFormat: 'json_object',
      maxTokens: Math.floor(modelConfig.maxTokens * 0.8),
      temperature: 0.7
    });

    // Process and validate response
    const parsedContent = PromptManager.extractJsonFromResponse(response.content);
    const mappedContent = PromptManager.processAIDudeResponse(parsedContent);
    
    // Validate against schema
    const validation = await this.validateAIDudeContent(mappedContent);

    return {
      success: true,
      content: mappedContent,
      validation,
      modelUsed: modelConfig,
      tokenUsage: response.tokenUsage,
      timestamp: new Date().toISOString(),
      methodology: 'ai_dude'
    };

  } catch (error) {
    log.ai('ai-dude-generation-error', `AI Dude generation failed: ${error.message}`, {
      toolUrl,
      error: error.message
    });

    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      methodology: 'ai_dude'
    };
  }
}

/**
 * Validate AI Dude generated content
 */
private async validateAIDudeContent(content: any): Promise<ValidationResult> {
  const issues: string[] = [];
  const warnings: string[] = [];

  // Required field validation
  const requiredFields = ['name', 'description', 'detailed_description', 'features', 'pros_and_cons'];
  for (const field of requiredFields) {
    if (!content[field]) {
      issues.push(`Missing required field: ${field}`);
    }
  }

  // Content length validation
  if (content.description && content.description.length > 500) {
    warnings.push('Description exceeds 500 character limit');
  }

  if (content.detailed_description) {
    const wordCount = content.detailed_description.split(' ').length;
    if (wordCount < 50 || wordCount > 300) {
      warnings.push(`Detailed description word count (${wordCount}) outside 50-300 range`);
    }
  }

  // Features validation
  if (content.features && (content.features.length < 3 || content.features.length > 8)) {
    warnings.push(`Features count (${content.features.length}) outside 3-8 range`);
  }

  // Category confidence validation
  if (content.ai_generation_metadata?.category_confidence < 0.75) {
    warnings.push('Low category confidence score');
  }

  return {
    isValid: issues.length === 0,
    issues,
    warnings,
    qualityScore: this.calculateQualityScore(content, issues, warnings),
    validatedAt: new Date().toISOString()
  };
}
```

### 3. Enhanced quickGenerate Function (`src/lib/ai/index.ts`)

Add this function to support AI Dude methodology:

```typescript
export const quickGenerateAIDude = async (
  content: string,
  url: string,
  options?: {
    priority?: any;
    complexity?: any;
    useAIDudeMethodology?: boolean;
  }
): Promise<any> => {
  const { AIContentGenerator } = await import('./content-generator');
  const generator = new AIContentGenerator();

  if (options?.useAIDudeMethodology) {
    return await generator.generateContentAIDude(content, url, options);
  } else {
    return await generator.generateContent(content, url, options);
  }
};
```

### 4. API Endpoint Updates

#### Update `/api/generate-content/route.ts`:

```typescript
export async function POST(request: NextRequest) {
  // ... existing validation code ...

  const { url, options = {}, methodology = 'standard' } = await request.json();

  // Set generation options
  const generationOptions: GenerationOptions = {
    complexity: options.complexity || 'medium',
    priority: options.priority || 'quality',
    contentQuality: options.contentQuality || 70,
    scrapingCost: options.scrapingCost || 0,
    maxRetries: 3,
    useAIDudeMethodology: methodology === 'ai_dude'
  };

  // Initialize AI content generator
  const aiGenerator = new AIContentGenerator();

  // Generate content using selected methodology
  let result;
  if (methodology === 'ai_dude') {
    result = await aiGenerator.generateContentAIDude(
      content,
      url,
      generationOptions
    );
  } else {
    result = await aiGenerator.generateContent(
      content,
      url,
      generationOptions
    );
  }

  // ... rest of existing code ...
}
```

#### Update `/api/admin/prompts/test/route.ts`:

```typescript
export async function POST(request: NextRequest) {
  // ... existing validation code ...

  // Enhanced sample data for AI Dude testing
  const sampleData = testData || {
    toolName: 'SnarkyAI',
    toolUrl: 'https://snarkyai.com',
    scrapedContent: `# SnarkyAI

An unapologetic AI tool that writes blog posts faster than you can type "coffee."

---

## Detailed Description

SnarkyAI is for people who can't stand fluff. It generates long-form blog posts by analyzing your outline and spitting out paragraphs that bite.

---

## Key Features

- Outline-to-Publish: Drop in bullet points, get a 2,000-word article in minutes.
- SnarkTone Adjuster: Dial the sarcasm up or down from "mild troll" to "full roast."
- Meme Inserter: Auto-embed relevant memes or GIFs if you ask nicely.

---

## Pros

- Saves time on writer's block—your brain can go watch Netflix.
- Hilariously irreverent; great for edgy brands.
- Built-in SEO keywords and meta tags—no more guesswork.

## Cons

- Not for serious academic stuff; it will roast your thesis.
- Meme Inserter sometimes posts the wrong GIF—oops.
- Pricing is per word, so if you forget to set a limit, expect sticker shock.

---

## Pricing

Paid: $0.02/word; monthly cap of $100.`
  };

  // Check if this is an AI Dude template
  const isAIDudeTemplate = promptTemplate.name?.includes('AI Dude') ||
                          promptTemplate.description?.includes('AI Dude');

  if (isAIDudeTemplate && promptTemplate.promptType === 'user') {
    // Use AI Dude methodology for testing
    aiResponse = await quickGenerateAIDude(
      sampleData.scrapedContent,
      sampleData.toolUrl,
      {
        priority: 'quality',
        complexity: 'medium',
        useAIDudeMethodology: true
      }
    );
  } else {
    // Use existing methodology
    aiResponse = await quickGenerate(
      processedPrompt,
      sampleData.toolUrl,
      {
        priority: 'quality',
        complexity: 'medium'
      }
    );
  }

  // ... rest of existing code ...
}
```

## Admin Interface Components

### 1. Methodology Selector Component

Create `src/components/admin/ContentGenerationMethodology.tsx`:

```typescript
interface ContentGenerationMethodologyProps {
  onMethodologyChange: (methodology: 'standard' | 'ai_dude') => void;
  currentMethodology: 'standard' | 'ai_dude';
}

export function ContentGenerationMethodology({ onMethodologyChange, currentMethodology }: ContentGenerationMethodologyProps) {
  return (
    <div className="bg-white border rounded-lg p-4">
      <h3 className="font-semibold mb-3">Content Generation Methodology</h3>

      <div className="space-y-3">
        <label className="flex items-center space-x-3">
          <input
            type="radio"
            name="methodology"
            value="standard"
            checked={currentMethodology === 'standard'}
            onChange={(e) => onMethodologyChange(e.target.value as 'standard')}
            className="text-orange-500"
          />
          <div>
            <div className="font-medium">Standard Generation</div>
            <div className="text-sm text-gray-600">
              Uses existing prompt templates and generation logic
            </div>
          </div>
        </label>

        <label className="flex items-center space-x-3">
          <input
            type="radio"
            name="methodology"
            value="ai_dude"
            checked={currentMethodology === 'ai_dude'}
            onChange={(e) => onMethodologyChange(e.target.value as 'ai_dude')}
            className="text-orange-500"
          />
          <div>
            <div className="font-medium">AI Dude Methodology</div>
            <div className="text-sm text-gray-600">
              Uses irreverent tone with specialized JSON schema mapping
            </div>
          </div>
        </label>
      </div>

      {currentMethodology === 'ai_dude' && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <h4 className="font-medium text-yellow-800">AI Dude Features:</h4>
          <ul className="text-sm text-yellow-700 mt-1 space-y-1">
            <li>• Irreverent, snarky tone throughout content</li>
            <li>• Enhanced JSON schema with confidence scoring</li>
            <li>• Specialized validation for content quality</li>
            <li>• Optimized for engaging, humorous descriptions</li>
          </ul>
        </div>
      )}
    </div>
  );
}
```

### 2. Integration Points

#### Update Content Generation Pages

Add methodology selector to existing content generation interfaces:

```typescript
// In admin content generation pages
const [methodology, setMethodology] = useState<'standard' | 'ai_dude'>('standard');

// Include in generation requests
const generateContent = async (url: string) => {
  const response = await fetch('/api/generate-content', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      url,
      methodology,
      options: {
        priority: 'quality',
        complexity: 'medium'
      }
    })
  });

  const result = await response.json();
  // Handle result...
};
```

## Testing Implementation

### 1. Unit Tests

Create `tests/lib/ai/prompt-manager-ai-dude.test.ts`:

```typescript
import { PromptManager } from '@/lib/ai/prompt-manager';

describe('PromptManager AI Dude Methodology', () => {
  describe('buildAIDudeSystemPrompt', () => {
    it('should include AI Dude persona and tone rules', () => {
      const schema = { name: 'string', description: 'string' };
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain('AI Dude');
      expect(prompt).toContain('irreverent, no-BS curator');
      expect(prompt).toContain('snarky, witty');
      expect(prompt).toContain('no corporate sugarcoating');
    });

    it('should inject database schema correctly', () => {
      const schema = { name: 'string', features: ['array'] };
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain(JSON.stringify(schema, null, 2));
    });
  });

  describe('processAIDudeResponse', () => {
    it('should map AI response to database schema', () => {
      const aiResponse = {
        name: 'Test Tool',
        description: 'A test tool',
        detailed_description: 'This is a detailed description',
        features: ['Feature 1', 'Feature 2'],
        pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] },
        category_primary: 'Content Generation',
        category_confidence: 0.95
      };

      const mapped = PromptManager.processAIDudeResponse(aiResponse);

      expect(mapped.name).toBe('Test Tool');
      expect(mapped.category_id).toBe('Content Generation');
      expect(mapped.ai_generation_metadata.methodology).toBe('ai_dude');
      expect(mapped.ai_generation_metadata.category_confidence).toBe(0.95);
    });
  });
});
```

### 2. Integration Tests

Create `tests/api/generate-content-ai-dude.test.ts`:

```typescript
describe('/api/generate-content with AI Dude methodology', () => {
  it('should generate content using AI Dude methodology', async () => {
    const response = await fetch('/api/generate-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: 'https://test-tool.com',
        methodology: 'ai_dude',
        options: { priority: 'quality' }
      })
    });

    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.methodology).toBe('ai_dude');
    expect(data.content.ai_generation_metadata.methodology).toBe('ai_dude');
  });
});
```

## Deployment Steps

### 1. Database Migration

```sql
-- Step 1: Add AI Dude prompt templates
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_system_v1', '{...}', 'prompt_template', 'AI Dude system prompt v1.0'),
('prompt_ai_dude_user_v1', '{...}', 'prompt_template', 'AI Dude user prompt v1.0');

-- Step 2: Verify templates were created
SELECT config_key, config_value->>'name' as name, config_value->>'promptType' as type
FROM system_configuration
WHERE config_key LIKE 'prompt_ai_dude%';
```

### 2. Code Deployment

1. **Deploy PromptManager updates**
2. **Deploy AIContentGenerator updates**
3. **Deploy API endpoint updates**
4. **Deploy admin interface components**
5. **Run tests to verify functionality**

### 3. Validation

```bash
# Test AI Dude template creation
curl -X POST http://localhost:3000/api/admin/prompts/test \
  -H "Content-Type: application/json" \
  -d '{"promptId": "ai_dude_template_id"}'

# Test content generation with AI Dude methodology
curl -X POST http://localhost:3000/api/generate-content \
  -H "Content-Type: application/json" \
  -d '{"url": "https://test-tool.com", "methodology": "ai_dude"}'
```

## Success Criteria

- [ ] AI Dude prompt templates stored in database
- [ ] PromptManager methods implemented and tested
- [ ] AIContentGenerator supports AI Dude methodology
- [ ] API endpoints accept methodology parameter
- [ ] Admin interface includes methodology selector
- [ ] All tests pass (unit, integration, e2e)
- [ ] Content generation produces valid JSON matching our schema
- [ ] Quality validation works for AI Dude content
- [ ] Backward compatibility maintained with existing system

## Next Steps

1. **Review and approve this implementation strategy**
2. **Begin Phase 1: Database preparation**
3. **Implement core functionality (PromptManager, AIContentGenerator)**
4. **Update API endpoints and admin interface**
5. **Comprehensive testing and validation**
6. **Production deployment with monitoring**

This implementation strategy provides a clear roadmap for integrating the AI Dude prompt methodology while maintaining system stability and ensuring high-quality content generation.
