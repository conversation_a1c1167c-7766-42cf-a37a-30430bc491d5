# AI Dude Prompt System - Implementation Strategy

## Overview

This document outlines the specific code changes and implementation steps required to integrate the AI Dude prompt methodology from `docs/ai-prompts example.md` into our existing AI tool directory system.

## Implementation Summary

### What We're Building

1. **Enhanced Prompt Templates**: Specialized templates following the AI Dude methodology
2. **Database Schema Integration**: Mapping example JSON structure to our tools table
3. **AI Generation Pipeline**: Modified content generation using the proven prompt pattern
4. **Admin Interface**: Enhanced prompt management for AI Dude templates
5. **Validation System**: Quality assurance for AI Dude generated content

### Key Benefits

- **Proven Methodology**: Uses the exact prompt structure from the working example
- **Consistent Tone**: Ensures irreverent, engaging content across all tools
- **Better Quality**: Enhanced validation and quality scoring
- **Seamless Integration**: Works alongside existing system without breaking changes

## Database Schema Modifications

### 1. Prompt Template Storage

The existing `system_configuration` table already supports prompt templates. We need to add AI Dude specific templates:

```sql
-- AI Dude Complete Content Generation System Prompt
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_complete_system', '{
  "name": "AI Dude Complete Content Generation System",
  "description": "System prompt for complete tool content generation with ALL database fields",
  "category": "content",
  "promptType": "system",
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:\n\n{DATABASE_SCHEMA}\n\n**Tone rules:**\n- Always write like a snarky, witty \"AI Dude.\"\n- Keep it punchy: no corporate sugarcoating.\n- Use contractions, slang, and street-smart humor.\n- Never apologize or say \"I'\''m sorry.\"\n- Write \"description\" as a one-sentence hook.\n- Write \"short_description\" as a punchy card summary (max 150 chars).\n- Make \"detailed_description\" engaging and informative (150-300 words).\n- Create \"meta_title\" and \"meta_description\" that are SEO-optimized but still snarky.\n- Ensure \"category_confidence\" is 0.90+ if obvious; 0.80-0.75 if guessing.\n\n**Field Requirements:**\n- **Required fields**: name, description, short_description, detailed_description, website, company, category_primary, features, pricing, pros_and_cons, faqs, hashtags, meta_title, meta_description\n- **Optional fields**: Fill when information is available in scraped content\n- **FAQ generation**: Create 3-5 relevant Q&As in AI Dude style with complete metadata\n- **SEO optimization**: meta_title (max 60 chars), meta_description (150-160 chars)\n\n**VERY IMPORTANT:**\n- Output exactly one JSON object.\n- Do not wrap it in backticks or code fences.\n- Do not add extra fields or comments.\n- If any section is missing, use appropriate defaults: \"\" for strings, [] for arrays, {} for objects.\n- Always format dates as YYYY-MM-DD.\n- Generate UUIDs for FAQ entries.\n- Include complete generated_content metadata.\n\nNow read the user content and produce the complete JSON with ALL required fields.",
  "variables": ["DATABASE_SCHEMA"],
  "validationRules": ["All required fields present", "Field length limits", "SEO optimization", "Complete FAQ structure"],
  "formatRequirements": "Complete JSON output with all database fields",
  "usage": 0
}', 'prompt_template', 'AI Dude complete content generation system prompt');

-- AI Dude Partial Generation with Context
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('prompt_ai_dude_partial_context', '{
  "name": "AI Dude Partial Generation with Context",
  "description": "User prompt for partial content generation with existing tool data context",
  "category": "partial",
  "promptType": "user",
  "template": "You are \"AI Dude,\" the irreverent, no-BS curator of AI tools. Generate ONLY the {sectionType} section for this tool in your signature snarky style.\n\n**Existing Tool Data (for context):**\n{existingToolData}\n\n**New Scraped Content:**\n{scrapedContent}\n\n**Tool URL:** {toolUrl}\n\n**Section to Generate:** {sectionType}\n\n**Section Requirements:**\n{sectionRequirements}\n\n**Instructions:**\n- Use the existing tool data for context and consistency\n- Focus ONLY on generating the {sectionType} section\n- Maintain consistency with existing tone and style\n- If updating existing content, improve and enhance it\n- Keep the irreverent, witty \"AI Dude\" voice throughout\n- For FAQs: Include complete metadata structure with UUIDs\n- For SEO: Optimize for search while maintaining personality\n\nOutput only the requested section in JSON format matching the database schema.",
  "variables": ["sectionType", "existingToolData", "scrapedContent", "toolUrl", "sectionRequirements"],
  "validationRules": ["Section-specific validation", "Consistency with existing data", "Complete field structure"],
  "formatRequirements": "JSON object containing only the requested section with proper schema structure",
  "usage": 0
}', 'prompt_template', 'AI Dude partial generation with context');
```

### 2. Complete JSON Schema Mapping

**COMPLETE** database schema mapping for ALL tools table fields:

| Database Field | AI Dude JSON Field | Type | Priority | Notes |
|----------------|-------------------|------|----------|-------|
| `name` | `name` | varchar(255) | **Required** | Tool display name |
| `description` | `description` | text | **Required** | Brief tool description |
| `short_description` | `short_description` | varchar(150) | **Required** | Card summary (max 150 chars) |
| `detailed_description` | `detailed_description` | text | **Required** | Comprehensive description (150-300 words) |
| `logo_url` | `logo_url` | text | Optional | Tool logo/icon URL |
| `website` | `website` | text | **Required** | Official tool website URL |
| `company` | `company` | varchar(255) | **Required** | Company/organization name |
| `category_id` | `category_primary` | varchar(255) | **Required** | Primary category |
| `subcategory` | `category_secondary` | varchar(255) | Optional | Secondary category |
| `features` | `features` | jsonb | **Required** | Array of tool features (3-8 items) |
| `screenshots` | `screenshots` | jsonb | Optional | Array of screenshot URLs |
| `pricing` | `pricing` | jsonb | **Required** | Pricing information object |
| `social_links` | `social_links` | jsonb | Optional | Social media links object |
| `pros_and_cons` | `pros_and_cons` | jsonb | **Required** | Pros and cons arrays (3-10 each) |
| `haiku` | `haiku` | jsonb | Optional | AI-generated haiku object |
| `hashtags` | `hashtags` | jsonb | **Required** | Array of hashtags/keywords (5-10) |
| `releases` | `releases` | jsonb | Optional | Version release information |
| `claim_info` | `claim_info` | jsonb | Optional | Tool claiming information |
| `faqs` | `faqs` | jsonb | **Required** | FAQ array with complete structure |
| `meta_title` | `meta_title` | varchar(255) | **Required** | SEO meta title (max 60 chars) |
| `meta_description` | `meta_description` | text | **Required** | SEO meta description (150-160 chars) |
| `generated_content` | `generated_content` | jsonb | System | AI generation metadata |

**Additional Context Fields for Partial Generation:**
| Context Field | Description | Usage |
|---------------|-------------|-------|
| `existingToolData` | Complete current tool data | **All partial generation templates** |
| `category_confidence` | AI confidence score (0.0-1.0) | Category classification accuracy |
| `updateReason` | Reason for content update | Partial generation context |
| `preserveFields` | Fields to keep unchanged | Selective content updates |

## Code Changes Required

### 1. Enhanced PromptManager (`src/lib/ai/prompt-manager.ts`)

Add these methods to the existing PromptManager class:

```typescript
/**
 * Build AI Dude system prompt with schema injection
 */
static buildAIDudeSystemPrompt(databaseSchema: any): string {
  const schemaString = JSON.stringify(databaseSchema, null, 2);
  
  return `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

${schemaString}

**Tone rules:**
- Always write like a snarky, witty "AI Dude."
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say "I'm sorry."
- Write "description" as a one-sentence hook.
- Ensure "category_confidence" is 0.90 or above if the category mapping is obvious; if you're guessing, use 0.80 or 0.75.

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing in the Markdown, leave that JSON field as an empty string (""), empty array ([]), or appropriate default ("unknown").
- Always format dates as YYYY-MM-DD.

Now read the user content and produce the JSON.`;
}

/**
 * Build AI Dude user prompt (raw content only)
 */
static buildAIDudeUserPrompt(scrapedContent: string, toolUrl: string): string {
  return scrapedContent; // Pure content as per example methodology
}

/**
 * Get complete database schema for AI Dude prompts (ALL fields)
 */
static getCompleteDatabaseSchema(): any {
  return {
    // Core identification fields
    name: "string (required, max 255 chars)",
    description: "string (required, brief description)",
    short_description: "string (required, max 150 chars for cards)",
    detailed_description: "string (required, 150-300 words)",

    // URLs and media
    logo_url: "string (optional, tool logo URL)",
    website: "string (required, official website URL)",
    screenshots: ["array of screenshot URLs (optional)"],

    // Company and categorization
    company: "string (required, company/organization name)",
    category_primary: "string (required, primary category)",
    category_secondary: "string (optional, secondary category)",
    category_confidence: "number (required, 0.0-1.0 confidence score)",

    // Core content fields
    features: ["array of 3-8 feature strings (required)"],
    pricing: {
      type: "Free|Paid|Freemium|Open Source (required)",
      plans: [{"name": "string", "price": "string", "features": ["array"]}],
      details: "string (pricing description)"
    },
    pros_and_cons: {
      pros: ["array of 3-10 pros (required)"],
      cons: ["array of 3-10 cons (required)"]
    },

    // Social and external links
    social_links: {
      twitter: "url or null",
      linkedin: "url or null",
      github: "url or null",
      facebook: "url or null",
      youtube: "url or null"
    },

    // Additional content
    hashtags: ["array of 5-10 hashtags (required)"],
    haiku: {
      lines: ["line1", "line2", "line3"],
      theme: "string"
    },
    releases: [
      {
        version: "string",
        releaseDate: "YYYY-MM-DD",
        changes: ["array of changes"]
      }
    ],

    // FAQ system with complete structure
    faqs: [
      {
        id: "uuid",
        question: "string (required)",
        answer: "string (required)",
        category: "general|pricing|features|support|getting-started",
        displayOrder: "number",
        priority: "number (1-10)",
        isActive: true,
        isFeatured: "boolean",
        source: "ai_generated",
        sourceMetadata: {
          aiModel: "string",
          confidence: "number (0.0-1.0)"
        },
        metaKeywords: "string"
      }
    ],

    // SEO fields
    meta_title: "string (required, max 60 chars, SEO optimized)",
    meta_description: "string (required, 150-160 chars, SEO optimized)",

    // Tool claiming system
    claim_info: {
      is_claimable: "boolean",
      claim_instructions: "string",
      verification_requirements: ["array"]
    },

    // System metadata (auto-generated)
    generated_content: {
      methodology: "ai_dude",
      generated_at: "ISO timestamp",
      model_used: "string",
      quality_score: "number (0-100)",
      validation_status: "string"
    }
  };
}

/**
 * Process complete AI Dude response and map to database schema (ALL fields)
 */
static processCompleteAIDudeResponse(aiResponse: any): any {
  const mapped = {
    // Core identification fields
    name: aiResponse.name || '',
    description: aiResponse.description || '',
    short_description: aiResponse.short_description || '',
    detailed_description: aiResponse.detailed_description || '',

    // URLs and media
    logo_url: aiResponse.logo_url || null,
    website: aiResponse.website || '',
    screenshots: aiResponse.screenshots || [],

    // Company and categorization
    company: aiResponse.company || '',
    category_id: aiResponse.category_primary || null,
    subcategory: aiResponse.category_secondary || null,

    // Core content fields
    features: aiResponse.features || [],
    pricing: aiResponse.pricing || { type: 'unknown', plans: [], details: '' },
    pros_and_cons: aiResponse.pros_and_cons || { pros: [], cons: [] },

    // Social and external links
    social_links: aiResponse.social_links || {},

    // Additional content
    hashtags: aiResponse.hashtags || [],
    haiku: aiResponse.haiku || { lines: [], theme: '' },
    releases: aiResponse.releases || [],

    // FAQ system with complete structure
    faqs: aiResponse.faqs || [],

    // SEO fields
    meta_title: aiResponse.meta_title || '',
    meta_description: aiResponse.meta_description || '',

    // Tool claiming system
    claim_info: aiResponse.claim_info || { is_claimable: false, claim_instructions: '', verification_requirements: [] }
  };

  // Add complete AI generation metadata
  mapped.generated_content = {
    methodology: 'ai_dude',
    generated_at: new Date().toISOString(),
    model_used: aiResponse.generated_content?.model_used || 'unknown',
    quality_score: aiResponse.generated_content?.quality_score || 0,
    validation_status: 'pending',
    category_confidence: aiResponse.category_confidence || 0.5,
    schema_version: '2.0',
    fields_generated: Object.keys(mapped).filter(key => mapped[key] !== '' && mapped[key] !== null &&
      (Array.isArray(mapped[key]) ? mapped[key].length > 0 : true))
  };

  return mapped;
}

/**
 * Process partial AI Dude response for specific sections
 */
static processPartialAIDudeResponse(aiResponse: any, sectionType: string, existingData: any = {}): any {
  const updated = { ...existingData };

  // Update only the specific section
  switch (sectionType) {
    case 'features':
      updated.features = aiResponse.features || [];
      break;
    case 'pricing':
      updated.pricing = aiResponse.pricing || { type: 'unknown', plans: [], details: '' };
      break;
    case 'pros_cons':
      updated.pros_and_cons = aiResponse.pros_and_cons || { pros: [], cons: [] };
      break;
    case 'seo':
      updated.meta_title = aiResponse.meta_title || '';
      updated.meta_description = aiResponse.meta_description || '';
      break;
    case 'faqs':
      updated.faqs = aiResponse.faqs || [];
      break;
    case 'social':
      updated.social_links = aiResponse.social_links || {};
      break;
    case 'company':
      updated.company = aiResponse.company || '';
      updated.website = aiResponse.website || updated.website;
      break;
    case 'content':
      updated.description = aiResponse.description || updated.description;
      updated.short_description = aiResponse.short_description || updated.short_description;
      updated.detailed_description = aiResponse.detailed_description || updated.detailed_description;
      break;
  }

  // Update generation metadata
  updated.generated_content = {
    ...updated.generated_content,
    last_updated: new Date().toISOString(),
    last_section_updated: sectionType,
    methodology: 'ai_dude_partial',
    partial_updates: [...(updated.generated_content?.partial_updates || []), {
      section: sectionType,
      updated_at: new Date().toISOString(),
      quality_score: aiResponse.quality_score || 0
    }]
  };

  return updated;
}
```

### 2. Enhanced AIContentGenerator (`src/lib/ai/content-generator.ts`)

Add this method to the existing AIContentGenerator class:

```typescript
/**
 * Generate content using AI Dude methodology
 */
async generateContentAIDude(
  scrapedContent: string,
  toolUrl: string,
  options: GenerationOptions = {}
): Promise<GenerationResult> {
  try {
    // Build AI Dude prompts
    const systemPrompt = PromptManager.buildAIDudeSystemPrompt(
      PromptManager.getAIDudeDatabaseSchema()
    );
    const userPrompt = PromptManager.buildAIDudeUserPrompt(scrapedContent, toolUrl);

    // Select optimal model
    const modelConfig = ModelSelector.selectOptimalModel({
      contentSize: PromptManager.calculateTokenCount(scrapedContent),
      complexity: options.complexity || 'medium',
      priority: options.priority || 'quality',
      features: ['json_output', 'large_context']
    });

    // Generate content
    const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;
    
    const response = await client.generateContent(systemPrompt, userPrompt, {
      model: modelConfig.model,
      responseFormat: 'json_object',
      maxTokens: Math.floor(modelConfig.maxTokens * 0.8),
      temperature: 0.7
    });

    // Process and validate response
    const parsedContent = PromptManager.extractJsonFromResponse(response.content);
    const mappedContent = PromptManager.processAIDudeResponse(parsedContent);
    
    // Validate against schema
    const validation = await this.validateAIDudeContent(mappedContent);

    return {
      success: true,
      content: mappedContent,
      validation,
      modelUsed: modelConfig,
      tokenUsage: response.tokenUsage,
      timestamp: new Date().toISOString(),
      methodology: 'ai_dude'
    };

  } catch (error) {
    log.ai('ai-dude-generation-error', `AI Dude generation failed: ${error.message}`, {
      toolUrl,
      error: error.message
    });

    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      methodology: 'ai_dude'
    };
  }
}

/**
 * Validate AI Dude generated content
 */
private async validateAIDudeContent(content: any): Promise<ValidationResult> {
  const issues: string[] = [];
  const warnings: string[] = [];

  // Required field validation
  const requiredFields = ['name', 'description', 'detailed_description', 'features', 'pros_and_cons'];
  for (const field of requiredFields) {
    if (!content[field]) {
      issues.push(`Missing required field: ${field}`);
    }
  }

  // Content length validation
  if (content.description && content.description.length > 500) {
    warnings.push('Description exceeds 500 character limit');
  }

  if (content.detailed_description) {
    const wordCount = content.detailed_description.split(' ').length;
    if (wordCount < 50 || wordCount > 300) {
      warnings.push(`Detailed description word count (${wordCount}) outside 50-300 range`);
    }
  }

  // Features validation
  if (content.features && (content.features.length < 3 || content.features.length > 8)) {
    warnings.push(`Features count (${content.features.length}) outside 3-8 range`);
  }

  // Category confidence validation
  if (content.ai_generation_metadata?.category_confidence < 0.75) {
    warnings.push('Low category confidence score');
  }

  return {
    isValid: issues.length === 0,
    issues,
    warnings,
    qualityScore: this.calculateQualityScore(content, issues, warnings),
    validatedAt: new Date().toISOString()
  };
}
```

### 3. Enhanced quickGenerate Function (`src/lib/ai/index.ts`)

Add this function to support AI Dude methodology:

```typescript
export const quickGenerateAIDude = async (
  content: string,
  url: string,
  options?: {
    priority?: any;
    complexity?: any;
    useAIDudeMethodology?: boolean;
  }
): Promise<any> => {
  const { AIContentGenerator } = await import('./content-generator');
  const generator = new AIContentGenerator();

  if (options?.useAIDudeMethodology) {
    return await generator.generateContentAIDude(content, url, options);
  } else {
    return await generator.generateContent(content, url, options);
  }
};
```

### 4. API Endpoint Updates

#### Update `/api/generate-content/route.ts`:

```typescript
export async function POST(request: NextRequest) {
  // ... existing validation code ...

  const { url, options = {}, methodology = 'standard' } = await request.json();

  // Set generation options
  const generationOptions: GenerationOptions = {
    complexity: options.complexity || 'medium',
    priority: options.priority || 'quality',
    contentQuality: options.contentQuality || 70,
    scrapingCost: options.scrapingCost || 0,
    maxRetries: 3,
    useAIDudeMethodology: methodology === 'ai_dude'
  };

  // Initialize AI content generator
  const aiGenerator = new AIContentGenerator();

  // Generate content using selected methodology
  let result;
  if (methodology === 'ai_dude') {
    result = await aiGenerator.generateContentAIDude(
      content,
      url,
      generationOptions
    );
  } else {
    result = await aiGenerator.generateContent(
      content,
      url,
      generationOptions
    );
  }

  // ... rest of existing code ...
}
```

#### Update `/api/admin/prompts/test/route.ts`:

```typescript
export async function POST(request: NextRequest) {
  // ... existing validation code ...

  // Enhanced sample data for AI Dude testing
  const sampleData = testData || {
    toolName: 'SnarkyAI',
    toolUrl: 'https://snarkyai.com',
    scrapedContent: `# SnarkyAI

An unapologetic AI tool that writes blog posts faster than you can type "coffee."

---

## Detailed Description

SnarkyAI is for people who can't stand fluff. It generates long-form blog posts by analyzing your outline and spitting out paragraphs that bite.

---

## Key Features

- Outline-to-Publish: Drop in bullet points, get a 2,000-word article in minutes.
- SnarkTone Adjuster: Dial the sarcasm up or down from "mild troll" to "full roast."
- Meme Inserter: Auto-embed relevant memes or GIFs if you ask nicely.

---

## Pros

- Saves time on writer's block—your brain can go watch Netflix.
- Hilariously irreverent; great for edgy brands.
- Built-in SEO keywords and meta tags—no more guesswork.

## Cons

- Not for serious academic stuff; it will roast your thesis.
- Meme Inserter sometimes posts the wrong GIF—oops.
- Pricing is per word, so if you forget to set a limit, expect sticker shock.

---

## Pricing

Paid: $0.02/word; monthly cap of $100.`
  };

  // Check if this is an AI Dude template
  const isAIDudeTemplate = promptTemplate.name?.includes('AI Dude') ||
                          promptTemplate.description?.includes('AI Dude');

  if (isAIDudeTemplate && promptTemplate.promptType === 'user') {
    // Use AI Dude methodology for testing
    aiResponse = await quickGenerateAIDude(
      sampleData.scrapedContent,
      sampleData.toolUrl,
      {
        priority: 'quality',
        complexity: 'medium',
        useAIDudeMethodology: true
      }
    );
  } else {
    // Use existing methodology
    aiResponse = await quickGenerate(
      processedPrompt,
      sampleData.toolUrl,
      {
        priority: 'quality',
        complexity: 'medium'
      }
    );
  }

  // ... rest of existing code ...
}
```

## Admin Interface Components

### 1. Methodology Selector Component

Create `src/components/admin/ContentGenerationMethodology.tsx`:

```typescript
interface ContentGenerationMethodologyProps {
  onMethodologyChange: (methodology: 'standard' | 'ai_dude') => void;
  currentMethodology: 'standard' | 'ai_dude';
}

export function ContentGenerationMethodology({ onMethodologyChange, currentMethodology }: ContentGenerationMethodologyProps) {
  return (
    <div className="bg-white border rounded-lg p-4">
      <h3 className="font-semibold mb-3">Content Generation Methodology</h3>

      <div className="space-y-3">
        <label className="flex items-center space-x-3">
          <input
            type="radio"
            name="methodology"
            value="standard"
            checked={currentMethodology === 'standard'}
            onChange={(e) => onMethodologyChange(e.target.value as 'standard')}
            className="text-orange-500"
          />
          <div>
            <div className="font-medium">Standard Generation</div>
            <div className="text-sm text-gray-600">
              Uses existing prompt templates and generation logic
            </div>
          </div>
        </label>

        <label className="flex items-center space-x-3">
          <input
            type="radio"
            name="methodology"
            value="ai_dude"
            checked={currentMethodology === 'ai_dude'}
            onChange={(e) => onMethodologyChange(e.target.value as 'ai_dude')}
            className="text-orange-500"
          />
          <div>
            <div className="font-medium">AI Dude Methodology</div>
            <div className="text-sm text-gray-600">
              Uses irreverent tone with specialized JSON schema mapping
            </div>
          </div>
        </label>
      </div>

      {currentMethodology === 'ai_dude' && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <h4 className="font-medium text-yellow-800">AI Dude Features:</h4>
          <ul className="text-sm text-yellow-700 mt-1 space-y-1">
            <li>• Irreverent, snarky tone throughout content</li>
            <li>• Enhanced JSON schema with confidence scoring</li>
            <li>• Specialized validation for content quality</li>
            <li>• Optimized for engaging, humorous descriptions</li>
          </ul>
        </div>
      )}
    </div>
  );
}
```

### 2. Integration Points

#### Update Content Generation Pages

Add methodology selector to existing content generation interfaces:

```typescript
// In admin content generation pages
const [methodology, setMethodology] = useState<'standard' | 'ai_dude'>('standard');

// Include in generation requests
const generateContent = async (url: string) => {
  const response = await fetch('/api/generate-content', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      url,
      methodology,
      options: {
        priority: 'quality',
        complexity: 'medium'
      }
    })
  });

  const result = await response.json();
  // Handle result...
};
```

## Testing Implementation

### 1. Unit Tests

Create `tests/lib/ai/prompt-manager-ai-dude.test.ts`:

```typescript
import { PromptManager } from '@/lib/ai/prompt-manager';

describe('PromptManager AI Dude Methodology', () => {
  describe('buildAIDudeSystemPrompt', () => {
    it('should include AI Dude persona and tone rules', () => {
      const schema = { name: 'string', description: 'string' };
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain('AI Dude');
      expect(prompt).toContain('irreverent, no-BS curator');
      expect(prompt).toContain('snarky, witty');
      expect(prompt).toContain('no corporate sugarcoating');
    });

    it('should inject database schema correctly', () => {
      const schema = { name: 'string', features: ['array'] };
      const prompt = PromptManager.buildAIDudeSystemPrompt(schema);

      expect(prompt).toContain(JSON.stringify(schema, null, 2));
    });
  });

  describe('processAIDudeResponse', () => {
    it('should map AI response to database schema', () => {
      const aiResponse = {
        name: 'Test Tool',
        description: 'A test tool',
        detailed_description: 'This is a detailed description',
        features: ['Feature 1', 'Feature 2'],
        pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] },
        category_primary: 'Content Generation',
        category_confidence: 0.95
      };

      const mapped = PromptManager.processAIDudeResponse(aiResponse);

      expect(mapped.name).toBe('Test Tool');
      expect(mapped.category_id).toBe('Content Generation');
      expect(mapped.ai_generation_metadata.methodology).toBe('ai_dude');
      expect(mapped.ai_generation_metadata.category_confidence).toBe(0.95);
    });
  });
});
```

### 2. Integration Tests

Create `tests/api/generate-content-ai-dude.test.ts`:

```typescript
describe('/api/generate-content with AI Dude methodology', () => {
  it('should generate content using AI Dude methodology', async () => {
    const response = await fetch('/api/generate-content', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: 'https://test-tool.com',
        methodology: 'ai_dude',
        options: { priority: 'quality' }
      })
    });

    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.methodology).toBe('ai_dude');
    expect(data.content.ai_generation_metadata.methodology).toBe('ai_dude');
  });
});
```

## Simplified Development Deployment

### 1. Immediate Database Updates (5 minutes)

```sql
-- Step 1: Add complete AI Dude prompt templates (copy from above)
-- Execute the complete INSERT statements for:
-- - prompt_ai_dude_complete_system
-- - prompt_ai_dude_partial_context

-- Step 2: Verify templates immediately
SELECT config_key, config_value->>'name' as name, config_value->>'promptType' as type
FROM system_configuration
WHERE config_key LIKE 'prompt_ai_dude%';

-- Step 3: Test template retrieval
SELECT config_value FROM system_configuration WHERE config_key = 'prompt_ai_dude_complete_system';
```

### 2. Direct Code Implementation (2-3 hours)

**No gradual deployment - implement all changes immediately:**

1. **Update PromptManager** - Add all new methods for complete field mapping
2. **Update AIContentGenerator** - Add complete and partial generation methods
3. **Update API endpoints** - Add methodology selection and all field support
4. **Update admin interface** - Add methodology selector and field options
5. **Test immediately** - No staging environment needed

### 3. Development Testing (30 minutes)

```bash
# Test complete content generation with all fields
curl -X POST http://localhost:3000/api/generate-content \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://test-tool.com",
    "methodology": "ai_dude",
    "options": {"priority": "quality", "generateAllFields": true}
  }'

# Test partial generation with existing data context
curl -X POST http://localhost:3000/api/generate-content \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://test-tool.com",
    "methodology": "ai_dude",
    "sectionType": "features",
    "existingToolData": {"name": "Test Tool", "description": "Existing description"},
    "options": {"partial": true}
  }'

# Test admin prompt management
curl -X GET http://localhost:3000/api/admin/prompts \
  -H "Content-Type: application/json"

# Test all database fields population
curl -X POST http://localhost:3000/api/admin/tools/validate-fields \
  -H "Content-Type: application/json" \
  -d '{"toolId": "test-tool-id"}'
```

### 4. Development Validation Checklist

**Immediate validation (no complex testing needed):**

- [ ] **Database**: AI Dude templates inserted and retrievable
- [ ] **Complete Generation**: All required fields populated in generated content
- [ ] **Partial Generation**: Existing tool data context working correctly
- [ ] **Field Mapping**: All 20+ database fields properly mapped
- [ ] **SEO Fields**: meta_title and meta_description generated correctly
- [ ] **FAQ Structure**: Complete FAQ metadata structure working
- [ ] **Admin Interface**: Methodology selector functional
- [ ] **Error Handling**: Basic error logging and handling working

**Quick fixes if issues found:**
- Update prompt templates directly in database
- Modify code and restart development server
- Test changes immediately
- No rollback planning needed - fix forward

## Success Criteria

- [ ] AI Dude prompt templates stored in database
- [ ] PromptManager methods implemented and tested
- [ ] AIContentGenerator supports AI Dude methodology
- [ ] API endpoints accept methodology parameter
- [ ] Admin interface includes methodology selector
- [ ] All tests pass (unit, integration, e2e)
- [ ] Content generation produces valid JSON matching our schema
- [ ] Quality validation works for AI Dude content
- [ ] Backward compatibility maintained with existing system

## Next Steps

1. **Review and approve this implementation strategy**
2. **Begin Phase 1: Database preparation**
3. **Implement core functionality (PromptManager, AIContentGenerator)**
4. **Update API endpoints and admin interface**
5. **Comprehensive testing and validation**
6. **Production deployment with monitoring**

This implementation strategy provides a clear roadmap for integrating the AI Dude prompt methodology while maintaining system stability and ensuring high-quality content generation.
